'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getTickStream } from '@/services/deriv-tick-stream';

export function WebSocketTest() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [tickCount, setTickCount] = useState(0);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-20), `[${timestamp}] ${message}`]);
  };

  const testConnection = () => {
    addLog('🔌 Starting WebSocket test...');
    const tickStream = getTickStream();
    
    const unsubscribe = tickStream.subscribe('Volatility 10 Index', {
      onTick: (tick) => {
        setTickCount(prev => prev + 1);
        addLog(`📊 Tick received: ${tick.price} at ${tick.time}`);
      },
      onError: (error) => {
        addLog(`❌ Error: ${error.message}`);
        setIsConnected(false);
      },
      onConnect: () => {
        addLog('✅ Connected to WebSocket');
        setIsConnected(true);
      },
      onDisconnect: () => {
        addLog('🔌 Disconnected from WebSocket');
        setIsConnected(false);
      }
    });

    // Clean up after 30 seconds
    setTimeout(() => {
      unsubscribe();
      addLog('🛑 Test completed - unsubscribed');
    }, 30000);
  };

  const clearLogs = () => {
    setLogs([]);
    setTickCount(0);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>WebSocket Connection Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={testConnection} disabled={isConnected}>
            Test Connection
          </Button>
          <Button onClick={clearLogs} variant="outline">
            Clear Logs
          </Button>
        </div>
        
        <div className="flex gap-4 text-sm">
          <span className={`px-2 py-1 rounded ${isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
          <span className="px-2 py-1 rounded bg-blue-100 text-blue-800">
            Ticks: {tickCount}
          </span>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
          <div className="font-mono text-xs space-y-1">
            {logs.length === 0 ? (
              <div className="text-gray-500">No logs yet. Click "Test Connection" to start.</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="whitespace-pre-wrap">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
